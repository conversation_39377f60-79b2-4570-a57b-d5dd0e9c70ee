{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["api/schema/**", "public"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "noUnusedTemplateLiteral": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "overrides": [{"include": ["*.vue"], "linter": {"rules": {"style": {"useConst": "off", "useImportType": "off"}}}}]}