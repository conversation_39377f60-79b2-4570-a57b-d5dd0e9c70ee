server:
  port: 8080
logging:
  file:
    path: /var/log
  level:
    dev:
      langchain4j: debug
    com:
      zl: debug
    org:
      springframework:
        security: debug
      flywaydb: debug
      jooq: debug
cors:
  allowedOrigins: ${ALLOWED_ORIGINS}
  allowedMethods: ${ALLOWED_METHODS}
  allowedHeaders: ${ALLOWED_HEADERS}
  allowedExposeHeaders: ${ALLOWED_EXPOSE_HEADERS}
spring:
  datasource:
    url: jdbc:postgresql://${DATABASE_HOST_PORT}/${DATABASE_DB}
    username: ${DATABASE_USER}
    password: ${DATABASE_PASSWORD}
  flyway:
    enabled: true
    locations: classpath:db/migration
    default-schema: ${DATABASE_DEFAULT_SCHEMA}
  servlet:
    multipart:
      max-file-size: 1MB
      max-request-size: 10MB
springdoc:
  swagger-ui:
    path: /swagger-ui.html
jwt:
  secret: ${JWT_SECRET:secret}
  expiration-min: ${JWT_EXPIRATION_MIN:100}
minio:
  endpoint: ${MINIO_ENDPOINT}
  access-key: ${MINIO_ROOT_USER}
  secret-key: ${MINIO_ROOT_PASSWORD}
  default-bucket: ${MINIO_DEFAULT_BUCKETS}
aop:
  logging:
    enabled: true
