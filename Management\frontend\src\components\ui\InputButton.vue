<template>
  <form class="w-full sm:max-w-xs mb-4">
    <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only">Search</label>
    <div class="relative">
      <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
        <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
          viewBox="0 0 20 20">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
        </svg>
      </div>
      <input type="search" id="default-search" v-model="bindInput" :class="[
          'block w-full ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500',
          size === 'sm' ? 'p-2.5 text-xs' : size === 'md' ? 'p-3 text-sm' : 'p-4 text-base'
        ]" :placeholder="placeholder" required />
      <button type="submit" :class="[
          'text-white absolute end-2.5 font-medium rounded-lg',
          size === 'sm' ? 'text-xs px-2.5 py-1.5 bottom-1.5' :
          size === 'md' ? 'text-sm px-3 py-2 bottom-1.5' :
          'text-base px-4 py-2.5 bottom-1.5',
          bgColor
        ]" @click.prevent="handleSubmitClick(bindInput)">{{ content }}</button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { z } from "zod";

const {
	placeholder,
	content,
	handleSubmit,
	size = "md",
	bgColor = "bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 ",
} = defineProps<{
	placeholder?: string;
	content: string;
	handleSubmit: (input: string) => void;
	size: "sm" | "md" | "lg";
	bgColor: string;
}>();

const bindInput = ref<string>();

const handleSubmitClick = (input?: string) => {
	const userSchema = z
		.string({
			message: "输入的内容不能为空",
		})
		.nonempty();
	const result = userSchema.parse(input);
	handleSubmit(result);
};
</script>
