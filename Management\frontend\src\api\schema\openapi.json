{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "AOP日志管理", "description": "AOP日志查看和管理接口"}], "paths": {"/scheduler/job/update": {"put": {"tags": ["scheduler-controller"], "operationId": "updateJob", "parameters": [{"name": "cron", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerKeyDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/knowledge/doc": {"put": {"tags": ["library-controller"], "operationId": "updateLibraryDoc", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocUpdateDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["library-controller"], "operationId": "deleteLibraryDoc", "parameters": [{"name": "libraryDocId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/ai/llm": {"put": {"tags": ["ai-controller"], "operationId": "updateLlm", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LlmVm"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/scheduler/trigger/resume": {"post": {"tags": ["scheduler-controller"], "operationId": "resume<PERSON><PERSON>ger", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerKeyDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/scheduler/trigger/pause": {"post": {"tags": ["scheduler-controller"], "operationId": "pauseTrigger", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerKeyDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/scheduler/job/trigger": {"post": {"tags": ["scheduler-controller"], "operationId": "triggerJob", "parameters": [{"name": "startAt", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobKeyDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/position": {"post": {"tags": ["position-controller"], "operationId": "upsertPosition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Position"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["position-controller"], "operationId": "deletePosition", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/knowledge/library": {"post": {"tags": ["library-controller"], "operationId": "upsertLibrary", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LibraryUpsertDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["library-controller"], "operationId": "deleteLibrary", "parameters": [{"name": "libraryId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/knowledge/doc/upload": {"post": {"tags": ["library-controller"], "operationId": "uploadLibraryDoc", "requestBody": {"content": {"application/json": {"schema": {"required": ["file", "libraryId"], "type": "object", "properties": {"libraryId": {"type": "string"}, "file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/iam/user": {"get": {"tags": ["identity-access-controller"], "operationId": "queryUserWithRolePermission", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserRolePermissionDto"}}}}}}, "post": {"tags": ["identity-access-controller"], "operationId": "upsertUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpsertDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["identity-access-controller"], "operationId": "deleteUser", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/iam/role": {"get": {"tags": ["identity-access-controller"], "operationId": "queryRoleWithPermission", "parameters": [{"name": "roleId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RoleRespDto"}}}}}}, "post": {"tags": ["identity-access-controller"], "operationId": "upsertRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUpsertDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["identity-access-controller"], "operationId": "deleteRole", "parameters": [{"name": "roleId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/iam/role/unbind": {"post": {"tags": ["identity-access-controller"], "operationId": "unBindRoleBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/role/bind": {"post": {"tags": ["identity-access-controller"], "operationId": "bindRoleBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/position/unbind": {"post": {"tags": ["identity-access-controller"], "operationId": "unBindPositionBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PositionBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/position/bind": {"post": {"tags": ["identity-access-controller"], "operationId": "bindPositionBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PositionBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/permission": {"post": {"tags": ["identity-access-controller"], "operationId": "upsertPermission", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionUpsertDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["identity-access-controller"], "operationId": "deletePermission", "parameters": [{"name": "permissionId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/iam/permission/unbind": {"post": {"tags": ["identity-access-controller"], "operationId": "unBindPermissionBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/permission/bind": {"post": {"tags": ["identity-access-controller"], "operationId": "bindPermissionBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/me": {"get": {"tags": ["identity-access-controller"], "operationId": "currentUser", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserRolePermissionDto"}}}}}}, "post": {"tags": ["identity-access-controller"], "operationId": "upsertMe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpsertDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/department/unbind": {"post": {"tags": ["identity-access-controller"], "operationId": "unBindDepartmentBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/department/bind": {"post": {"tags": ["identity-access-controller"], "operationId": "bindDepartmentBy", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentBindDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/iam/avatar/upload": {"post": {"tags": ["identity-access-controller"], "operationId": "uploadAvatar", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/department": {"post": {"tags": ["department-controller"], "operationId": "upsertDepartment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Department"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["department-controller"], "operationId": "deleteDepartment", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/auth/sign-up": {"post": {"tags": ["sign-controller"], "operationId": "signUp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignUpDto"}}}, "required": true}, "responses": {"201": {"description": "Created"}}}}, "/auth/sign-out": {"post": {"tags": ["sign-controller"], "operationId": "signOut", "responses": {"200": {"description": "OK"}}}}, "/auth/sign-in": {"post": {"tags": ["sign-controller"], "operationId": "signIn", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/ai/chat": {"post": {"tags": ["ai-controller"], "operationId": "chat", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/event-stream": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/ai/chat/refresh": {"post": {"tags": ["ai-controller"], "operationId": "createNewConversation", "responses": {"200": {"description": "OK"}}}}, "/ai/action/search": {"post": {"tags": ["ai-controller"], "operationId": "searchAction", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}}, "/ai/action/execute": {"post": {"tags": ["ai-controller"], "operationId": "actionExecute", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/event-stream": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/scheduler/page-query": {"get": {"tags": ["scheduler-controller"], "operationId": "pageQuery", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "queryDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/QueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListJobTriggerDto"}}}}}}}, "/position/query": {"get": {"tags": ["position-controller"], "operationId": "queryPositions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Position"}}}}}}}}, "/position/page-query": {"get": {"tags": ["position-controller"], "operationId": "pageQueryPositions", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "positionQueryDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PositionQueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListPositionRespDto"}}}}}}}, "/knowledge/segments": {"get": {"tags": ["library-controller"], "operationId": "queryLibraryDocSegments", "parameters": [{"name": "libraryDocId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LibraryDocSegment"}}}}}}}}, "/knowledge/libraries": {"get": {"tags": ["library-controller"], "operationId": "queryLibraries", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Library"}}}}}}}}, "/knowledge/docs": {"get": {"tags": ["library-controller"], "operationId": "queryLibraryDocs", "parameters": [{"name": "libraryId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LibraryDoc"}}}}}}}}, "/iam/users": {"get": {"tags": ["identity-access-controller"], "operationId": "queryUsers", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "userQueryDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/UserQueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListUserRolePermissionDto"}}}}}}}, "/iam/roles": {"get": {"tags": ["identity-access-controller"], "operationId": "queryRoles", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/RoleQueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListRoleRespDto"}}}}}}}, "/iam/permissions": {"get": {"tags": ["identity-access-controller"], "operationId": "queryPermissions", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "permissionQueryDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PermissionQueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListPermissionRespDto"}}}}}}}, "/department/query-sub": {"get": {"tags": ["department-controller"], "operationId": "querySubDepartment", "parameters": [{"name": "id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentWithParentDto"}}}}}}}}, "/department/query-available": {"get": {"tags": ["department-controller"], "operationId": "queryAvailableParentDepartmentsBy", "parameters": [{"name": "id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}}}}}}}, "/department/page-query": {"get": {"tags": ["department-controller"], "operationId": "pageQueryDepartments", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "departmentQueryDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/DepartmentQueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListDepartmentRespDto"}}}}}}}, "/aop-log/{id}": {"get": {"tags": ["AOP日志管理"], "summary": "查询日志详情", "description": "根据ID查询单条日志的详细信息", "operationId": "getAopLogById", "parameters": [{"name": "id", "in": "path", "description": "日志ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AopLogRespDto"}}}}}}, "delete": {"tags": ["AOP日志管理"], "summary": "删除单条日志", "description": "根据ID删除单条日志", "operationId": "deleteAopLog", "parameters": [{"name": "id", "in": "path", "description": "日志ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/aop-log/page-query": {"get": {"tags": ["AOP日志管理"], "summary": "分页查询AOP日志", "description": "支持多种条件筛选的分页查询", "operationId": "pageQueryAopLogs", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "queryDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AopLogQueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListAopLogRespDto"}}}}}}}, "/ai/llm/page-query": {"get": {"tags": ["ai-controller"], "operationId": "pageQueryLlm", "parameters": [{"name": "pageRequestDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDto"}}, {"name": "llmQueryDto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/LlmQueryDto"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResponseDtoListLlmVm"}}}}}}}, "/aop-log/before": {"delete": {"tags": ["AOP日志管理"], "summary": "删除指定时间前的日志", "description": "删除指定时间之前的所有日志", "operationId": "deleteLogsBeforeTime", "parameters": [{"name": "beforeTime", "in": "query", "description": "截止时间", "required": true, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/aop-log/batch": {"delete": {"tags": ["AOP日志管理"], "summary": "批量删除日志", "description": "根据ID列表批量删除日志", "operationId": "deleteAopLogs", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "日志ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/ai/action/user": {"delete": {"tags": ["ai-controller"], "operationId": "deleteUser_1", "parameters": [{"name": "username", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/ai/action/role": {"delete": {"tags": ["ai-controller"], "operationId": "deleteRole_1", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/ai/action/position": {"delete": {"tags": ["ai-controller"], "operationId": "deletePosition_1", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/ai/action/permission": {"delete": {"tags": ["ai-controller"], "operationId": "deletePermission_1", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/ai/action/department": {"delete": {"tags": ["ai-controller"], "operationId": "deleteDepartment_1", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"TriggerKeyDto": {"required": ["group", "name"], "type": "object", "properties": {"name": {"type": "string"}, "group": {"type": "string"}}}, "DocUpdateDto": {"required": ["enable", "id", "libId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "libId": {"type": "integer", "format": "int64"}, "enable": {"type": "boolean"}}}, "LlmVm": {"required": ["<PERSON><PERSON><PERSON><PERSON>", "enable", "id", "modelName", "name", "priority", "type", "url"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "modelName": {"type": "string"}, "type": {"type": "string"}, "apiKey": {"type": "string"}, "url": {"type": "string"}, "enable": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}}}, "JobKeyDto": {"required": ["group", "name"], "type": "object", "properties": {"name": {"type": "string"}, "group": {"type": "string"}}}, "Position": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "LibraryUpsertDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "description": {"type": "string"}}}, "UserUpsertDto": {"required": ["enable", "username"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "password": {"type": "string"}, "enable": {"type": "boolean"}, "avatar": {"type": "string"}}}, "RoleUpsertDto": {"required": ["code", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "RoleBindDto": {"required": ["roleIds", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "roleIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PositionBindDto": {"required": ["positionIds", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "positionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PermissionUpsertDto": {"required": ["code", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "PermissionBindDto": {"required": ["permissionIds", "roleId"], "type": "object", "properties": {"roleId": {"type": "integer", "format": "int64"}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "DepartmentBindDto": {"required": ["departmentIds", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "departmentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "Department": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}}}, "SignUpDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}, "SignInDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}, "ChatDto": {"required": ["message", "mode"], "type": "object", "properties": {"mode": {"type": "string", "enum": ["NORMAL", "WITH_LIBRARY"]}, "libraryId": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "PageRequestDto": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int64"}, "sortBy": {"type": "string", "description": "排序字段", "example": "name:asc,age:desc"}, "offset": {"type": "integer", "format": "int64"}, "sortFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortFieldObject"}}}}, "SortFieldObject": {"type": "object", "properties": {"name": {"type": "string"}, "order": {"type": "string", "enum": ["ASC", "DESC", "DEFAULT"]}}}, "QueryDto": {"type": "object", "properties": {"name": {"type": "string"}}}, "JobTriggerDto": {"type": "object", "properties": {"name": {"type": "string"}, "group": {"type": "string"}, "className": {"type": "string"}, "jobDataMap": {"type": "object", "additionalProperties": {"type": "object"}}, "triggerName": {"type": "string"}, "triggerGroup": {"type": "string"}, "schedulerType": {"type": "string"}, "cronExpression": {"type": "string"}, "startTime": {"type": "integer", "format": "int64"}, "endTime": {"type": "integer", "format": "int64"}, "nextFireTime": {"type": "integer", "format": "int64"}, "previousFireTime": {"type": "integer", "format": "int64"}, "triggerState": {"type": "string"}, "triggerJobDataMap": {"type": "object", "additionalProperties": {"type": "object"}}}}, "PageResponseDtoListJobTriggerDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/JobTriggerDto"}}}}, "PositionQueryDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "bindState": {"type": "string", "enum": ["BIND", "UNBIND", "ALL"]}}}, "PageResponseDtoListPositionRespDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PositionRespDto"}}}}, "PositionRespDto": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "isBound": {"type": "boolean"}}}, "LibraryDocSegment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "docId": {"type": "integer", "format": "int64"}, "embeddingId": {"type": "string"}, "content": {"type": "string"}, "tokenUsage": {"type": "integer", "format": "int32"}}}, "Library": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "description": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}}}, "JSON": {"type": "object"}, "LibraryDoc": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "libId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "identify": {"type": "string"}, "path": {"type": "string"}, "meta": {"$ref": "#/components/schemas/JSON"}, "enable": {"type": "boolean"}, "status": {"type": "string", "enum": ["SUCCESS", "INDEXING"]}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}}, "UserQueryDto": {"type": "object", "properties": {"username": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}}}, "PageResponseDtoListUserRolePermissionDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserRolePermissionDto"}}}}, "PermissionRespDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "name": {"type": "string"}, "isBound": {"type": "boolean"}}}, "RoleRespDto": {"required": ["code", "id", "isBound", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "name": {"type": "string"}, "isBound": {"type": "boolean"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionRespDto"}}}}, "UserRolePermissionDto": {"required": ["createTime", "enable", "id", "username"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "password": {"type": "string", "writeOnly": true}, "avatar": {"type": "string"}, "enable": {"type": "boolean"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/RoleRespDto"}}, "createTime": {"type": "string", "format": "date-time"}, "permissions": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/PermissionRespDto"}}}}, "RoleQueryDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "roleCode": {"type": "string"}, "roleName": {"type": "string"}, "roleIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "bindState": {"type": "string", "enum": ["BIND", "UNBIND", "ALL"]}}}, "PageResponseDtoListRoleRespDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleRespDto"}}}}, "PermissionQueryDto": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int64"}, "permissionId": {"type": "integer", "format": "int64"}, "permissionCode": {"type": "string"}, "permissionName": {"type": "string"}, "permissionIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "bindState": {"type": "string", "enum": ["BIND", "UNBIND", "ALL"]}}}, "PageResponseDtoListPermissionRespDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionRespDto"}}}}, "DepartmentWithParentDto": {"required": ["id", "name", "parentId", "parentName", "path"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "parentName": {"type": "string"}, "path": {"type": "string"}}}, "DepartmentQueryDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "enable": {"type": "boolean"}, "bindState": {"type": "string", "enum": ["BIND", "UNBIND", "ALL"]}}}, "DepartmentRespDto": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "parentName": {"type": "string"}, "isBound": {"type": "boolean"}}}, "PageResponseDtoListDepartmentRespDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentRespDto"}}}}, "AopLogRespDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "className": {"type": "string"}, "methodName": {"type": "string"}, "methodArgs": {"type": "string"}, "returnValue": {"type": "string"}, "executionTime": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}, "errorMessage": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "ipAddress": {"type": "string"}, "userAgent": {"type": "string"}, "curl": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}}}, "AopLogQueryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "className": {"type": "string"}, "methodName": {"type": "string"}, "success": {"type": "boolean"}, "userId": {"type": "integer", "format": "int64"}, "ipAddress": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "minExecutionTime": {"type": "integer", "format": "int64"}, "maxExecutionTime": {"type": "integer", "format": "int64"}}}, "PageResponseDtoListAopLogRespDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AopLogRespDto"}}}}, "LlmQueryDto": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}}}, "PageResponseDtoListLlmVm": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LlmVm"}}}}}}}