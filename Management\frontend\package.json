{"name": "frontend", "version": "1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "generate:api": "openapi-typescript ./src/api/schema/openapi.json -o ./src/api/types/schema.d.ts", "format": "biome format --write .", "lint": "biome lint --write .", "check": "biome check --write .", "test:unit": "vitest", "test:e2e": "playwright test", "test:ts": "tsc --noEmit", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@tailwindcss/vite": "^4.0.14", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "^13.0.0", "apexcharts": "^3.46.0", "compressorjs": "^1.2.1", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "flowbite": "^3.1.2", "github-markdown-css": "^5.8.1", "marked": "^15.0.12", "openapi-fetch": "^0.13.5", "pinia": "^3.0.1", "tailwindcss": "^4.1.11", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@faker-js/faker": "^9.6.0", "@playwright/test": "^1.51.0", "@tsconfig/node22": "^22.0.0", "@types/dompurify": "^3.0.5", "@types/marked": "^5.0.2", "@types/node": "^22.13.9", "@vitejs/plugin-vue": "^5.2.1", "@vitest/browser": "^3.0.9", "@vue/tsconfig": "^0.7.0", "msw": "^2.8.2", "npm-run-all2": "^7.0.2", "openapi-typescript": "^7.6.1", "playwright": "^1.51.1", "typescript": "~5.8.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.0.8", "vitest-browser-vue": "^0.2.0", "vue-tsc": "^2.2.8"}, "msw": {"workerDirectory": ["public"]}}